package co.com.gedsys.gedsys2bpmengine.infrastructure.amqp;

import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.interfaces.AbstractRabbitMQListener;
import co.com.gedsys.gedsys2bpmengine.constant.TaskVariableName;
import co.com.gedsys.gedsys2bpmengine.datastructucture.OrchestratorProcedureMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.cibseven.bpm.engine.RuntimeService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class ProcedureOrchestratedListener extends AbstractRabbitMQListener<Map<String, Object>> {

    private final RuntimeService runtimeService;
    private final ObjectMapper objectMapper;

    public ProcedureOrchestratedListener(
            RabbitTemplate rabbitTemplate,
            RuntimeService runtimeService,
            ObjectMapper objectMapper) {
        super(rabbitTemplate);
        this.runtimeService = runtimeService;
        this.objectMapper = objectMapper;
    }

    @RabbitListener(queues = QueueName.PROCEDURES, containerFactory = "primaryRabbitListenerContainerFactory")
    @Override
    public void processMessage(Map<String, Object> payload, Message message, Channel channel,
                               long deliveryTag) {
        super.processMessage(payload, message, channel, deliveryTag);
    }

    @Override
    protected void handleMessageProcessing(Map<String, Object> messageMap) {
        try {
            OrchestratorProcedureMessage orchestratorMessage = objectMapper.convertValue(messageMap, OrchestratorProcedureMessage.class);

            log.info("Received message: {}", orchestratorMessage);

            runtimeService.createMessageCorrelation(orchestratorMessage.correlationMessage())
                    .processInstanceId(orchestratorMessage.processInstanceId())
                    .setVariable(TaskVariableName.MESSAGE, messageMap)
                    .correlate();
        } catch (Exception e) {
            log.error("Error processing orchestrator message: {}", e.getMessage(), e);

            final var EXECUTION_ID_KEY = "executionId";
            final var executionId = messageMap.containsKey(EXECUTION_ID_KEY) ? messageMap.get(EXECUTION_ID_KEY).toString()
                    : "executionId not found";

            runtimeService.createIncident(
                    "MessageCorrelationHandler",
                    executionId,
                    "Error al procesar mensaje de orquestación",
                    e.getMessage());
        }
    }
}

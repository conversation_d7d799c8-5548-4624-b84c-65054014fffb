package co.com.gedsys.gedsys2bpmengine.datastructucture;

import java.util.Map;

public record OrchestratorProcedureMessage(
        String workflowExecutionId, //from n8n
        String executionId, //from camunda
        String processInstanceId,
        String tenantId,
        String resumeUrl, //from n8n
        String publishedAt,
        String correlationMessage,
        Map<String, Object> variables) {
}
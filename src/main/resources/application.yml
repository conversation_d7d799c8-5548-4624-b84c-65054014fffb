spring:
  application:
    name: gedsys2-bpm-engine
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    driver-class-name: org.postgresql.Driver
  web:
    resources:
      add-mappings: false
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USER:guest}
    password: ${RABBITMQ_PASSWORD:guest}
    virtual-host: ${app.tenant-id}
    listener:
      simple:
        acknowledge-mode: manual
        default-requeue-rejected: true

camunda:
  bpm:
    admin-user:
      id: admin
      password: ${CAMUNDA_ADMIN_PASSWORD:admin}
      email: <EMAIL>
      first-name: Admin
      last-name: Admin

server:
  port: 8080

n8n:
  base-url: ${N8N_BASE_URL:https://automation.gedsys.dev/webhook}


app:
  tenant-id: ${APP_TENANT_ID}
  rabbitmq:
    secondary-virtual-host: /